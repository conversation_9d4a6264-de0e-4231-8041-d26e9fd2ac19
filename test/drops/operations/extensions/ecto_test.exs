defmodule Drops.Operations.Extensions.EctoTest do
  use Drops.DataCase, async: true

  alias Drops.Operations.Extensions.Ecto, as: EctoExtension

  describe "enabled?/1" do
    test "returns true when repo is configured" do
      opts = [repo: Drops.TestRepo]
      assert Ecto.enabled?(opts) == true
    end

    test "returns false when repo is nil" do
      opts = [repo: nil]
      assert Ecto.enabled?(opts) == false
    end

    test "returns false when repo is not configured" do
      opts = [type: :command]
      assert Ecto.enabled?(opts) == false
    end

    test "returns false for empty options" do
      opts = []
      assert Ecto.enabled?(opts) == false
    end
  end

  describe "validate/2" do
    test "returns changeset unchanged when given a changeset" do
      changeset = Ecto.Changeset.change(%Test.Ecto.UserSchema{}, %{name: "<PERSON>"})

      result = Ecto.validate(TestModule, changeset)

      assert result == changeset
    end

    test "returns params unchanged when given params" do
      params = %{name: "<PERSON>", email: "<EMAIL>"}

      result = Ecto.validate(TestModule, params)

      assert result == params
    end
  end

  describe "cast_changeset/3" do
    test "returns changeset unchanged by default" do
      changeset = Ecto.Changeset.change(%Test.Ecto.UserSchema{}, %{name: "John"})
      params = %{name: "Jane"}

      result = Ecto.cast_changeset(TestModule, params, changeset)

      assert result == changeset
    end
  end

  describe "changeset/2" do
    defmodule TestOperationWithSchema do
      def schema do
        %{
          meta: %{source_schema: Test.Ecto.UserSchema},
          keys: []
        }
      end
    end

    test "creates changeset from operation schema and params" do
      params = %{name: "John", email: "<EMAIL>"}

      result = Ecto.changeset(TestOperationWithSchema, params)

      assert %Ecto.Changeset{} = result
      assert result.data.__struct__ == Test.Ecto.UserSchema
      assert result.changes == params
    end
  end

  describe "persist/2" do
    defmodule TestOperationWithRepo do
      def __repo__, do: Drops.TestRepo

      def changeset(params) do
        Ecto.Changeset.change(%Test.Ecto.UserSchema{}, params)
      end
    end

    @tag ecto_schemas: [Test.Ecto.UserSchema]
    test "inserts changeset using configured repo" do
      params = %{name: "John", email: "<EMAIL>"}

      result = Ecto.persist(TestOperationWithRepo, params)

      assert {:ok, %Test.Ecto.UserSchema{}} = result
    end

    defmodule TestOperationWithoutRepo do
      def __repo__, do: nil

      def changeset(params) do
        Ecto.Changeset.change(%Test.Ecto.UserSchema{}, params)
      end
    end

    test "raises error when no repo is configured" do
      params = %{name: "John", email: "<EMAIL>"}

      assert_raise RuntimeError, "No repo configured for this operation", fn ->
        Ecto.persist(TestOperationWithoutRepo, params)
      end
    end
  end

  describe "enhanced_validate/3" do
    defmodule TestOperationEmpty do
      def schema, do: %{keys: [], meta: %{}}
      def prepare(params), do: params
    end

    test "returns prepared params when schema has no keys" do
      params = %{name: "John"}

      result = Ecto.enhanced_validate(TestOperationEmpty, params, :command)

      assert {:ok, params} == result
    end

    defmodule TestOperationNoEctoSchema do
      def schema, do: %{keys: [:name], meta: %{source_schema: nil}}
      def prepare(params), do: params
      def conform(params), do: {:ok, params}
    end

    test "uses standard validation when no Ecto schema is configured" do
      params = %{name: "John"}

      result = Ecto.enhanced_validate(TestOperationNoEctoSchema, params, :command)

      assert {:ok, params} == result
    end

    defmodule TestOperationWithEctoSchema do
      def schema, do: %{keys: [:name], meta: %{source_schema: Test.Ecto.UserSchema}}
      def prepare(params), do: params
      def conform(params), do: {:ok, params}

      def changeset(params) do
        %Test.Ecto.UserSchema{}
        |> Ecto.Changeset.cast(params, [:name, :email])
        |> Ecto.Changeset.validate_required([:name])
      end

      def cast_changeset(_params, changeset), do: changeset
      def validate(changeset), do: changeset
    end

    test "uses Ecto validation pipeline when Ecto schema is configured" do
      params = %{name: "John", email: "<EMAIL>"}

      result = Ecto.enhanced_validate(TestOperationWithEctoSchema, params, :command)

      assert {:ok, params} == result
    end

    defmodule TestOperationWithInvalidChangeset do
      def schema, do: %{keys: [:name], meta: %{source_schema: Test.Ecto.UserSchema}}
      def prepare(params), do: params
      def conform(params), do: {:ok, params}

      def changeset(params) do
        %Test.Ecto.UserSchema{}
        |> Ecto.Changeset.cast(params, [:name, :email])
        |> Ecto.Changeset.validate_required([:name, :email])
      end

      def cast_changeset(_params, changeset), do: changeset
      def validate(changeset), do: changeset
    end

    test "returns changeset error when validation fails for non-form operations" do
      # missing required email
      params = %{name: "John"}

      result = Ecto.enhanced_validate(TestOperationWithInvalidChangeset, params, :command)

      assert {:error, %Ecto.Changeset{valid?: false}} = result
    end

    test "returns changeset with action set for form operations" do
      # missing required email
      params = %{name: "John"}

      result = Ecto.enhanced_validate(TestOperationWithInvalidChangeset, params, :form)

      assert {:error, %Ecto.Changeset{valid?: false, action: :validate}} = result
    end

    defmodule TestOperationWithSchemaErrors do
      def schema, do: %{keys: [:name], meta: %{source_schema: Test.Ecto.UserSchema}}
      def prepare(params), do: params
      def conform(_params), do: {:error, [%{path: [:name], text: "is required"}]}

      def changeset(params) do
        %Test.Ecto.UserSchema{}
        |> Ecto.Changeset.cast(params, [:name, :email])
      end
    end

    test "converts schema errors to changeset errors for form operations" do
      params = %{}

      result = Ecto.enhanced_validate(TestOperationWithSchemaErrors, params, :form)

      assert {:error, %Ecto.Changeset{action: :validate, errors: errors}} = result
      assert {:name, {"is required", []}} in errors
    end

    test "returns schema errors unchanged for non-form operations" do
      params = %{}

      result = Ecto.enhanced_validate(TestOperationWithSchemaErrors, params, :command)

      assert {:error, [%{path: [:name], text: "is required"}]} = result
    end

    defmodule TestOperationWithStringFieldErrors do
      def schema, do: %{keys: [:name], meta: %{source_schema: Test.Ecto.UserSchema}}
      def prepare(params), do: params
      def conform(_params), do: {:error, [%{path: ["name"], text: "is invalid"}]}

      def changeset(params) do
        %Test.Ecto.UserSchema{}
        |> Ecto.Changeset.cast(params, [:name, :email])
      end
    end

    test "converts string field names to atoms in schema errors for form operations" do
      params = %{}

      result = Ecto.enhanced_validate(TestOperationWithStringFieldErrors, params, :form)

      assert {:error, %Ecto.Changeset{action: :validate, errors: errors}} = result
      assert {:name, {"is invalid", []}} in errors
    end

    defmodule TestOperationWithNestedErrors do
      def schema, do: %{keys: [:name], meta: %{source_schema: Test.Ecto.UserSchema}}
      def prepare(params), do: params

      def conform(_params),
        do: {:error, [%{path: [:address, :street], text: "is required"}]}

      def changeset(params) do
        %Test.Ecto.UserSchema{}
        |> Ecto.Changeset.cast(params, [:name, :email])
      end
    end

    test "flattens nested field paths to first level for form operations" do
      params = %{}

      result = Ecto.enhanced_validate(TestOperationWithNestedErrors, params, :form)

      assert {:error, %Ecto.Changeset{action: :validate, errors: errors}} = result
      assert {:address, {"is required", []}} in errors
    end
  end

  describe "extension behavior" do
    test "extend_using_macro/1 returns quoted code" do
      opts = [repo: Drops.TestRepo]
      result = Ecto.extend_using_macro(opts)

      # Should return quoted code (AST)
      assert is_tuple(result)
    end

    test "extend_operation_runtime/1 returns quoted code" do
      opts = [repo: Drops.TestRepo]
      result = Ecto.extend_operation_runtime(opts)

      # Should return quoted code (AST)
      assert is_tuple(result)
    end

    test "extend_operation_definition/1 returns quoted code" do
      opts = [repo: Drops.TestRepo]
      result = Ecto.extend_operation_definition(opts)

      # Should return quoted code (AST)
      assert is_tuple(result)
    end

    test "extend_operation_definition/1 conditionally includes persist function" do
      # With repo
      opts_with_repo = [repo: Drops.TestRepo]
      result_with_repo = Ecto.extend_operation_definition(opts_with_repo)
      assert is_tuple(result_with_repo)

      # Without repo
      opts_without_repo = [repo: nil]
      result_without_repo = Ecto.extend_operation_definition(opts_without_repo)
      assert is_tuple(result_without_repo)
    end
  end
end
